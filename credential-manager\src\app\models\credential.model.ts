export interface Credential {
  id?: string;
  userId: string;
  title: string;
  type: CredentialType;
  category: string;
  username?: string;
  password?: string;
  apiKey?: string;
  url?: string;
  notes?: string;
  isValid?: boolean;
  lastValidated?: Date;
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
}

export enum CredentialType {
  PASSWORD = 'password',
  API_KEY = 'api_key',
  TOKEN = 'token',
  CERTIFICATE = 'certificate',
  OTHER = 'other'
}

export interface CredentialCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
}

export interface ValidationResult {
  isValid: boolean;
  message: string;
  lastChecked: Date;
}

export interface CredentialFilter {
  type?: CredentialType;
  category?: string;
  searchTerm?: string;
  tags?: string[];
  isValid?: boolean;
}
