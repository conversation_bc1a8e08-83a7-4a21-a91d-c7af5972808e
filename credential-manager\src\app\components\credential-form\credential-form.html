<div class="form-container">
  <div class="form-card">
    <div class="form-header">
      <h2>{{ isEditMode ? 'Edit Credential' : 'Add New Credential' }}</h2>
      <p>{{ isEditMode ? 'Update your credential information' : 'Securely store your credential information' }}</p>
    </div>

    <form [formGroup]="credentialForm" (ngSubmit)="onSubmit()">
      <!-- Title -->
      <div class="form-group">
        <label for="title">Title *</label>
        <input
          type="text"
          id="title"
          formControlName="title"
          placeholder="e.g., Gmail Account, OpenAI API"
          [class.error]="credentialForm.get('title')?.invalid && credentialForm.get('title')?.touched">
        <div class="error-message" *ngIf="credentialForm.get('title')?.invalid && credentialForm.get('title')?.touched">
          Title is required
        </div>
      </div>

      <!-- Type -->
      <div class="form-group">
        <label for="type">Type *</label>
        <select id="type" formControlName="type">
          <option *ngFor="let type of credentialTypes" [value]="type">
            {{ type | titlecase }}
          </option>
        </select>
      </div>

      <!-- Category -->
      <div class="form-group">
        <label for="category">Category *</label>
        <select id="category" formControlName="category">
          <option value="">Select a category</option>
          <option *ngFor="let category of predefinedCategories" [value]="category">
            {{ category }}
          </option>
        </select>
      </div>

      <!-- Username -->
      <div class="form-group">
        <label for="username">Username/Email</label>
        <input
          type="text"
          id="username"
          formControlName="username"
          placeholder="Enter username or email">
      </div>

      <!-- Password (shown for password type) -->
      <div class="form-group" *ngIf="isPasswordType">
        <label for="password">Password *</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          placeholder="Enter password"
          [class.error]="credentialForm.get('password')?.invalid && credentialForm.get('password')?.touched">
        <div class="error-message" *ngIf="credentialForm.get('password')?.invalid && credentialForm.get('password')?.touched">
          Password is required
        </div>
      </div>

      <!-- API Key (shown for API key/token types) -->
      <div class="form-group" *ngIf="isApiKeyType">
        <label for="apiKey">API Key/Token *</label>
        <textarea
          id="apiKey"
          formControlName="apiKey"
          placeholder="Enter your API key or token"
          rows="3"
          [class.error]="credentialForm.get('apiKey')?.invalid && credentialForm.get('apiKey')?.touched"></textarea>
        <div class="error-message" *ngIf="credentialForm.get('apiKey')?.invalid && credentialForm.get('apiKey')?.touched">
          API Key is required
        </div>
      </div>

      <!-- URL -->
      <div class="form-group">
        <label for="url">Website URL</label>
        <input
          type="url"
          id="url"
          formControlName="url"
          placeholder="https://example.com">
      </div>

      <!-- Notes -->
      <div class="form-group">
        <label for="notes">Notes</label>
        <textarea
          id="notes"
          formControlName="notes"
          placeholder="Additional notes or information"
          rows="3"></textarea>
      </div>

      <!-- Tags -->
      <div class="form-group">
        <label for="tags">Tags</label>
        <input
          type="text"
          id="tags"
          formControlName="tags"
          placeholder="work, important, backup (comma separated)">
        <small class="help-text">Separate tags with commas</small>
      </div>

      <!-- Error Message -->
      <div class="error-message" *ngIf="error">
        {{ error }}
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button type="button" class="btn btn-secondary" (click)="cancel()">
          Cancel
        </button>
        <button
          type="submit"
          class="btn btn-primary"
          [disabled]="credentialForm.invalid || loading">
          <span *ngIf="loading">{{ isEditMode ? 'Updating...' : 'Saving...' }}</span>
          <span *ngIf="!loading">{{ isEditMode ? 'Update Credential' : 'Save Credential' }}</span>
        </button>
      </div>
    </form>
  </div>
</div>
