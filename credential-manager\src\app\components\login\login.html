<div class="login-container">
  <div class="login-card">
    <h2>{{ isSignUp ? 'Create Account' : 'Sign In' }}</h2>
    <p class="subtitle">{{ isSignUp ? 'Join to manage your credentials securely' : 'Welcome back to your secure vault' }}</p>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          placeholder="Enter your email"
          [class.error]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
        <div class="error-message" *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
          <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          placeholder="Enter your password"
          [class.error]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
        <div class="error-message" *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
          <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div class="error-message" *ngIf="error">
        {{ error }}
      </div>

      <button
        type="submit"
        class="submit-btn"
        [disabled]="loginForm.invalid || loading">
        <span *ngIf="loading">{{ isSignUp ? 'Creating Account...' : 'Signing In...' }}</span>
        <span *ngIf="!loading">{{ isSignUp ? 'Create Account' : 'Sign In' }}</span>
      </button>
    </form>

    <div class="toggle-mode">
      <p>
        {{ isSignUp ? 'Already have an account?' : "Don't have an account?" }}
        <button type="button" class="link-btn" (click)="toggleMode()">
          {{ isSignUp ? 'Sign In' : 'Sign Up' }}
        </button>
      </p>
    </div>
  </div>
</div>
