import { Component, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-login',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './login.html',
  styleUrl: './login.scss'
})
export class Login {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);

  loginForm: FormGroup;
  isSignUp = false;
  loading = false;
  error = '';

  constructor() {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  async onSubmit() {
    if (this.loginForm.valid) {
      this.loading = true;
      this.error = '';

      const { email, password } = this.loginForm.value;

      try {
        if (this.isSignUp) {
          await this.authService.signUp(email, password);
        } else {
          await this.authService.signIn(email, password);
        }
      } catch (error: any) {
        this.error = error.message || 'Authentication failed';
      } finally {
        this.loading = false;
      }
    }
  }

  toggleMode() {
    this.isSignUp = !this.isSignUp;
    this.error = '';
  }
}
